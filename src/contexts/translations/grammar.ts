import { TranslationDict } from './translation-dict';

export const grammarTranslations: TranslationDict = {
	'grammar.practice_title': {
		EN: 'Grammar Practice',
		VI: '<PERSON>y<PERSON>n tập ngữ pháp'
	},
	'grammar.practice_description': {
		EN: 'Practice identifying and correcting grammar errors in text passages to improve your language skills.',
		VI: '<PERSON><PERSON><PERSON>n tập nhận diện và sửa lỗi ngữ pháp trong đoạn văn để cải thiện kỹ năng ngôn ngữ.'
	},	'grammar.generation_failed_title': {
		EN: 'Generation Failed',
		VI: 'Tạo thất bại'
	},	'grammar.error_density.label': {
		EN: 'Error Density',
		VI: 'Mật độ lỗi'
	},
	'grammar.error_density.low': {
		EN: 'Low',
		VI: 'Thấp'
	},
	'grammar.error_density.medium': {
		EN: 'Medium',
		VI: 'Trung bình'
	},
	'grammar.error_density.high': {
		EN: 'High',
		VI: 'Cao'
	},	'grammar.view_result_button': {
		EN: 'Generate Practice',
		VI: 'Tạo bài tập'
	},
	'grammar.generating': {
		EN: 'Generating...',
		VI: 'Đang tạo...'
	},
	'grammar.generating_paragraphs': {
		EN: 'Generating practice paragraphs...',
		VI: 'Đang tạo đoạn văn luyện tập...'
	}};
